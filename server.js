const express = require('express');
const cors = require('cors');
const path = require('path');
const https = require('https');

const app = express();
const PORT = process.env.PORT || 3000;

// API Configuration
const API_CONFIG = {
    apiKey: "infip-e080069d",
    url: "https://api.infip.pro/v1/images/generations",
    model: "img3"
};

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname)));

// Serve the main HTML file
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Proxy endpoint for image generation
app.post('/api/generate', async (req, res) => {
    try {
        const { prompt, num_images, size } = req.body;
        
        // Validate input
        if (!prompt || !prompt.trim()) {
            return res.status(400).json({ error: 'Prompt is required' });
        }
        
        const payload = {
            model: API_CONFIG.model,
            prompt: prompt.trim(),
            num_images: parseInt(num_images) || 1,
            size: size || "1024x1024"
        };
        
        console.log('Generating images with payload:', payload);

        // Make request to Infip API using https module
        const postData = JSON.stringify(payload);
        const url = new URL(API_CONFIG.url);

        const options = {
            hostname: url.hostname,
            port: 443,
            path: url.pathname,
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${API_CONFIG.apiKey}`,
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            }
        };

        const apiResponse = await new Promise((resolve, reject) => {
            const req = https.request(options, (apiRes) => {
                let data = '';

                apiRes.on('data', (chunk) => {
                    data += chunk;
                });

                apiRes.on('end', () => {
                    try {
                        const jsonData = JSON.parse(data);
                        resolve({ status: apiRes.statusCode, data: jsonData, rawData: data });
                    } catch (parseError) {
                        resolve({ status: apiRes.statusCode, data: null, rawData: data });
                    }
                });
            });

            req.on('error', (error) => {
                reject(error);
            });

            req.write(postData);
            req.end();
        });

        if (apiResponse.status !== 200) {
            console.error('API Error:', apiResponse.status, apiResponse.rawData);
            return res.status(apiResponse.status).json({
                error: `API request failed: ${apiResponse.status}`,
                details: apiResponse.rawData
            });
        }

        console.log('API Response received:', apiResponse.data);

        // Return the response to the client
        res.json(apiResponse.data);
        
    } catch (error) {
        console.error('Server error:', error);
        res.status(500).json({ 
            error: 'Internal server error',
            details: error.message
        });
    }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error('Unhandled error:', err);
    res.status(500).json({ error: 'Something went wrong!' });
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 ImageCraft AI Server running on http://localhost:${PORT}`);
    console.log(`📁 Serving files from: ${__dirname}`);
    console.log(`🎨 Ready to generate amazing images!`);
});

module.exports = app;
