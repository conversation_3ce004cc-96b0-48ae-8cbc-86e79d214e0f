{"name": "imagecraft-ai", "version": "1.0.0", "description": "Modern AI Image Generation Website", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "install-deps": "npm install"}, "keywords": ["ai", "image-generation", "web-app", "nodejs", "express"], "author": "ImageCraft AI", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}