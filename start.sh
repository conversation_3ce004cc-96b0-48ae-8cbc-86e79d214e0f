#!/bin/bash

echo "========================================"
echo "   ImageCraft AI - Starting Server"
echo "========================================"
echo

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "ERROR: Node.js is not installed!"
    echo "Please install Node.js from https://nodejs.org/"
    echo
    exit 1
fi

echo "Node.js version:"
node --version
echo

# Check if dependencies are installed
if [ ! -d "node_modules" ]; then
    echo "Installing dependencies..."
    npm install
    echo
fi

echo "Starting ImageCraft AI server..."
echo
echo "Server will be available at: http://localhost:3000"
echo "Press Ctrl+C to stop the server"
echo

npm start
