@echo off
echo ========================================
echo    ImageCraft AI - Starting Server
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed!
    echo Please install Node.js from https://nodejs.org/
    echo.
    pause
    exit /b 1
)

echo Node.js version:
node --version
echo.

REM Check if dependencies are installed
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    echo.
)

echo Starting ImageCraft AI server...
echo.
echo Server will be available at: http://localhost:3100
echo Press Ctrl+C to stop the server
echo.

npm start
