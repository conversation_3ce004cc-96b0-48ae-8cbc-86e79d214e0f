// API Configuration - Using local proxy server
const API_CONFIG = {
    url: "/api/generate" // Local proxy endpoint
};

// DOM Elements
const imageForm = document.getElementById('imageForm');
const promptInput = document.getElementById('prompt');
const numImagesSelect = document.getElementById('numImages');
const imageSizeSelect = document.getElementById('imageSize');
const generateBtn = document.getElementById('generateBtn');
const loadingState = document.getElementById('loadingState');
const resultsSection = document.getElementById('resultsSection');
const imageGrid = document.getElementById('imageGrid');
const generateMoreBtn = document.getElementById('generateMoreBtn');

// State Management
let isGenerating = false;
let currentImages = [];

// Event Listeners
imageForm.addEventListener('submit', handleFormSubmit);
generateMoreBtn.addEventListener('click', handleGenerateMore);

// Form Submit Handler
async function handleFormSubmit(e) {
    e.preventDefault();
    
    if (isGenerating) return;
    
    const prompt = promptInput.value.trim();
    if (!prompt) {
        showNotification('Please enter a prompt', 'error');
        return;
    }
    
    await generateImages(prompt);
}

// Generate More Handler
function handleGenerateMore() {
    const prompt = promptInput.value.trim();
    if (prompt) {
        generateImages(prompt);
    }
}

// Main Image Generation Function
async function generateImages(prompt) {
    try {
        setGeneratingState(true);

        const payload = {
            prompt: prompt,
            num_images: parseInt(numImagesSelect.value),
            size: imageSizeSelect.value
        };

        const response = await fetch(API_CONFIG.url, {
            method: 'POST',
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify(payload)
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.images && data.images.length > 0) {
            displayImages(data.images, prompt);
            showNotification('Images generated successfully!', 'success');
        } else if (data.error) {
            throw new Error(data.error);
        } else {
            throw new Error('No images received from API');
        }

    } catch (error) {
        console.error('Error generating images:', error);
        let errorMessage = 'Failed to generate images. Please try again.';

        if (error.message.includes('Failed to fetch')) {
            errorMessage = 'Connection error. Make sure the server is running.';
        } else if (error.message) {
            errorMessage = error.message;
        }

        showNotification(errorMessage, 'error');
    } finally {
        setGeneratingState(false);
    }
}

// Set Generating State
function setGeneratingState(generating) {
    isGenerating = generating;
    
    if (generating) {
        generateBtn.disabled = true;
        generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span>Generating...</span>';
        loadingState.style.display = 'block';
        resultsSection.style.display = 'none';
    } else {
        generateBtn.disabled = false;
        generateBtn.innerHTML = '<i class="fas fa-wand-magic-sparkles"></i><span>Generate Images</span>';
        loadingState.style.display = 'none';
    }
}

// Display Generated Images
function displayImages(images, prompt) {
    currentImages = images;
    imageGrid.innerHTML = '';
    
    images.forEach((imageUrl, index) => {
        const imageItem = createImageItem(imageUrl, prompt, index);
        imageGrid.appendChild(imageItem);
    });
    
    resultsSection.style.display = 'block';
    resultsSection.scrollIntoView({ behavior: 'smooth' });
}

// Create Image Item Element
function createImageItem(imageUrl, prompt, index) {
    const imageItem = document.createElement('div');
    imageItem.className = 'image-item';
    
    imageItem.innerHTML = `
        <img src="${imageUrl}" alt="${prompt}" loading="lazy" onerror="handleImageError(this)">
        <div class="image-actions">
            <button class="action-btn" onclick="downloadImage('${imageUrl}', '${prompt}', ${index})" title="Download">
                <i class="fas fa-download"></i>
            </button>
            <button class="action-btn" onclick="copyImageUrl('${imageUrl}')" title="Copy URL">
                <i class="fas fa-copy"></i>
            </button>
            <button class="action-btn" onclick="openImageFullscreen('${imageUrl}')" title="View Fullscreen">
                <i class="fas fa-expand"></i>
            </button>
        </div>
    `;
    
    return imageItem;
}

// Handle Image Loading Error
function handleImageError(img) {
    img.parentElement.innerHTML = `
        <div style="padding: 40px; text-align: center; background: #f8f9fa; border-radius: 8px;">
            <i class="fas fa-exclamation-triangle" style="font-size: 2rem; color: #ffc107; margin-bottom: 10px;"></i>
            <p>Failed to load image</p>
        </div>
    `;
}

// Download Image
async function downloadImage(imageUrl, prompt, index) {
    try {
        const response = await fetch(imageUrl);
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `${sanitizeFilename(prompt)}_${index + 1}.png`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
        
        showNotification('Image downloaded successfully!', 'success');
    } catch (error) {
        console.error('Download error:', error);
        showNotification('Failed to download image', 'error');
    }
}

// Copy Image URL
async function copyImageUrl(imageUrl) {
    try {
        await navigator.clipboard.writeText(imageUrl);
        showNotification('Image URL copied to clipboard!', 'success');
    } catch (error) {
        console.error('Copy error:', error);
        showNotification('Failed to copy URL', 'error');
    }
}

// Open Image in Fullscreen
function openImageFullscreen(imageUrl) {
    const modal = document.createElement('div');
    modal.className = 'fullscreen-modal';
    modal.innerHTML = `
        <div class="modal-backdrop" onclick="closeFullscreen()">
            <img src="${imageUrl}" alt="Generated Image" onclick="event.stopPropagation()">
            <button class="close-btn" onclick="closeFullscreen()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    document.body.appendChild(modal);
    document.body.style.overflow = 'hidden';
}

// Close Fullscreen Modal
function closeFullscreen() {
    const modal = document.querySelector('.fullscreen-modal');
    if (modal) {
        document.body.removeChild(modal);
        document.body.style.overflow = 'auto';
    }
}

// Utility Functions
function sanitizeFilename(filename) {
    return filename.replace(/[^a-z0-9]/gi, '_').toLowerCase().substring(0, 50);
}

// Notification System
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${getNotificationIcon(type)}"></i>
        <span>${message}</span>
    `;
    
    document.body.appendChild(notification);
    
    // Trigger animation
    setTimeout(() => notification.classList.add('show'), 100);
    
    // Auto remove
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

function getNotificationIcon(type) {
    const icons = {
        success: 'check-circle',
        error: 'exclamation-circle',
        info: 'info-circle',
        warning: 'exclamation-triangle'
    };
    return icons[type] || icons.info;
}

// Prompt Suggestions
const promptSuggestions = [
    "A majestic lion in a field of wildflowers, digital art, highly detailed",
    "Futuristic cityscape at sunset, cyberpunk style, neon lights",
    "Peaceful mountain lake with reflection, oil painting style",
    "Abstract geometric patterns in vibrant colors",
    "Vintage steam locomotive in a misty forest",
    "Underwater coral reef with tropical fish, photorealistic",
    "Medieval castle on a hilltop, fantasy art style",
    "Space station orbiting Earth, sci-fi concept art"
];

// Add prompt suggestion functionality
promptInput.addEventListener('focus', showPromptSuggestions);

function showPromptSuggestions() {
    if (promptInput.value.trim() === '') {
        const randomSuggestion = promptSuggestions[Math.floor(Math.random() * promptSuggestions.length)];
        promptInput.placeholder = randomSuggestion;
    }
}

// Keyboard shortcuts
document.addEventListener('keydown', (e) => {
    if (e.ctrlKey && e.key === 'Enter') {
        if (!isGenerating && promptInput.value.trim()) {
            handleFormSubmit(e);
        }
    }
});

// Initialize app
document.addEventListener('DOMContentLoaded', () => {
    console.log('ImageCraft AI initialized');
    showPromptSuggestions();
});
