# ImageCraft AI - Modern Image Generation Website

A beautiful, modern web application for generating AI images using the Infip API. Create stunning visuals from text descriptions with an intuitive and responsive interface.

## Features

✨ **Modern Design**
- Sleek gradient backgrounds and glass-morphism effects
- Responsive design that works on all devices
- Smooth animations and transitions
- Professional typography with Inter font

🎨 **Image Generation**
- Generate 1-4 images at once
- Multiple size options (Square, Landscape, Portrait)
- Real-time generation status
- Error handling and loading states

🖼️ **Image Management**
- View generated images in a responsive grid
- Download images with custom filenames
- Copy image URLs to clipboard
- Fullscreen image viewing
- Hover effects and action buttons

🚀 **User Experience**
- Smart prompt suggestions
- Keyboard shortcuts (Ctrl+Enter to generate)
- Toast notifications for feedback
- Smooth scrolling to results
- Loading animations

## Getting Started

1. **Open the website**: Simply open `index.html` in your web browser
2. **Enter a prompt**: Describe what you want to see in the text area
3. **Configure settings**: Choose number of images and size
4. **Generate**: Click the "Generate Images" button or press Ctrl+Enter
5. **Enjoy**: View, download, or share your generated images

## File Structure

```
├── index.html          # Main HTML structure
├── style.css           # Modern CSS styling with animations
├── script.js           # JavaScript functionality and API integration
└── README.md           # This file
```

## API Configuration

The website uses the Infip API for image generation. The API configuration is set in `script.js`:

```javascript
const API_CONFIG = {
    apiKey: "infip-e080069d",
    url: "https://api.infip.pro/v1/images/generations",
    model: "img3"
};
```

## Customization

### Changing Colors
The main gradient colors can be modified in `style.css`:
```css
background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
```

### Adding New Features
- Modify `script.js` to add new functionality
- Update `style.css` for styling changes
- Extend `index.html` for new UI elements

## Browser Compatibility

- Chrome/Edge: Full support
- Firefox: Full support
- Safari: Full support
- Mobile browsers: Responsive design

## Tips for Better Results

1. **Be Descriptive**: Use detailed prompts for better results
2. **Include Style**: Mention art styles like "digital art", "oil painting", "photorealistic"
3. **Add Details**: Include lighting, mood, and composition details
4. **Use Keywords**: Terms like "highly detailed", "4K", "masterpiece" can improve quality

## Example Prompts

- "A majestic lion in a field of wildflowers, digital art, highly detailed"
- "Futuristic cityscape at sunset, cyberpunk style, neon lights"
- "Peaceful mountain lake with reflection, oil painting style"
- "Abstract geometric patterns in vibrant colors"

## Troubleshooting

**Images not loading?**
- Check your internet connection
- Verify the API key is valid
- Try refreshing the page

**Generation taking too long?**
- Complex prompts may take longer
- Try simpler descriptions
- Check the API status

## License

This project is open source and available under the MIT License.

---

Enjoy creating amazing AI-generated images with ImageCraft AI! 🎨✨
