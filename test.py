import requests

api_key = "infip-e080069d"
url = "https://api.infip.pro/v1/images/generations"

headers = {
    "Authorization": f"Bearer {api_key}",
    "Content-Type": "application/json"
}

payload = {
    "model": "img3",
    "prompt": "A majestic lion in a field of wildflowers",
    "n": 4,
    "size": "1024x1024"
}

response = requests.post(url, headers=headers, json=payload)

if response.status_code == 200:
    print(response.json())
else:
    print(f"Error: {response.status_code}", response.text)